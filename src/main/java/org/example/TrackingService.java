package org.example;

import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.location.Location;
import android.location.LocationListener;
import android.location.LocationManager;
import android.os.IBinder;
import androidx.core.app.ActivityCompat;
import android.Manifest;

public class TrackingService extends Service {
    private LocationManager locationManager;
    private LocationListener locationListener;

    @Override
    public void onCreate() {
        super.onCreate();

        locationManager = (LocationManager) getSystemService(Context.LOCATION_SERVICE);
        locationListener = new LocationListener() {
            @Override
            public void onLocationChanged(Location location) {
                sendLocationToParent(location);
            }
        };

        // Request location updates
        if (ActivityCompat.checkSelfPermission(this,
                Manifest.permission.ACCESS_FINE_LOCATION) ==
                PackageManager.PERMISSION_GRANTED) {

            locationManager.requestLocationUpdates(
                    LocationManager.GPS_PROVIDER,
                    30000,  // 30 seconds
                    50,     // 50 meters
                    locationListener
            );
        }
    }

    private void sendLocationToParent(Location location) {
        // Send location to parent's device via Firebase or server
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }
}