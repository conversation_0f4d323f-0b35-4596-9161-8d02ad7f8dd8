package org.example;

public class TrackingService extends Service {
    private LocationManager locationManager;
    private LocationListener locationListener;

    @Override
    public void onCreate() {
        super.onCreate();

        locationManager = (LocationManager) getSystemService(Context.LOCATION_SERVICE);
        locationListener = new LocationListener() {
            @Override
            public void onLocationChanged(Location location) {
                sendLocationToParent(location);
            }
        };

        // Request location updates
        if (ActivityCompat.checkSelfPermission(this,
                Manifest.permission.ACCESS_FINE_LOCATION) ==
                PackageManager.PERMISSION_GRANTED) {

            locationManager.requestLocationUpdates(
                    LocationManager.GPS_PROVIDER,
                    30000,  // 30 seconds
                    50,     // 50 meters
                    locationListener
            );
        }
    }

    private void sendLocationToParent(Location location) {
        // Send location to parent's device via Firebase or server
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }
}